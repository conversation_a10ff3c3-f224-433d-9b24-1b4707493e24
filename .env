AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=+zj4epu8belJCYMMjE7zBVFL5QqUQ6yj4TZPkAOY
AWS_REGION=ap-south-1
S3_BUCKET_NAME=cdnexp
CLOUDFRONT_DOMAIN=d3ajpe89oedzo8.cloudfront.net
CLOUDFRONT_PUBLIC_KEY_ID=K2CDRJ39NULY90
CLOUDFRONT_PRIVATE_KEY_PATH=cloudfront-private-key.pem

# Redis Configuration for One-Time Access Tokens (Legacy - for server-side tokens)
REDIS_URL=redis://localhost:6379

# DynamoDB Configuration for Lambda@Edge One-Time Access
DYNAMODB_TABLE_NAME=cloudfront-one-time-tokens