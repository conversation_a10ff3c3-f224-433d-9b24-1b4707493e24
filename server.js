import express from "express";
import dotenv from "dotenv";
import multer from "multer";
import { generateSignedUrl, generateOneTimeUrl, generateOneTimeAccessUrl } from "./cloudfrontSigner.js";
import { uploadFileToS3 } from "./s3Upload.js";

dotenv.config();
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configure multer for file uploads
const upload = multer({ dest: "uploads/" });


// Health check endpoint
app.get("/", (req, res) => {
    res.json({
        message: "CloudFront S3 Upload Service with One-Time Access Control",
        endpoints: {
            upload: "POST /upload",
            oneTimeUrl: "GET /get-one-time-url?file=<filepath>&expires=<seconds>",
            signedUrl: "GET /get-signed-url?file=<filepath> (legacy)",
            access: "GET /access/<token>",
            preview: "GET /preview/<token>",
            download: "GET /download/<token>",
            health: "GET /health",
            stats: "GET /admin/stats"
        },
        features: [
            "One-time access URLs with token validation",
            "Redis-based token management",
            "Automatic token expiration",
            "Access logging and monitoring"
        ]
    });
});

app.post("/upload", upload.single("file"), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
        }

        const key = await uploadFileToS3(req.file);
        const baseUrl = `${req.protocol}://${req.get('host')}`;

        // Generate one-time access URL using Lambda@Edge
        const oneTimeUrlData = await generateOneTimeAccessUrl(`/${key}`, 300, {
            uploadedBy: req.ip,
            originalFilename: req.file.originalname,
            fileSize: req.file.size,
            mimeType: req.file.mimetype
        });

        res.json({
            message: "File uploaded successfully",
            key,
            s3Url: `https://${process.env.S3_BUCKET_NAME}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`,
            oneTimeAccess: {
                url: oneTimeUrlData.oneTimeUrl,
                token: oneTimeUrlData.token,
                expiresAt: oneTimeUrlData.expiresAt,
                expiresIn: oneTimeUrlData.expiresIn
            }
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Upload failed" });
    }
});



// Legacy endpoint: get signed URL for a file (deprecated)
app.get("/get-signed-url", (req, res) => {
    const { file, expires } = req.query; // e.g., ?file=/mydocs/file.pdf&expires=10

    if (!file) {
        return res.status(400).json({ error: "file query param required" });
    }

    const expiresIn = expires ? parseInt(expires) : 60; // Default 1 minute, allow custom

    try {
        const signedUrl = generateSignedUrl(file, expiresIn);
        res.json({
            warning: "This endpoint is deprecated. Use /get-one-time-url for one-time access control.",
            signedUrl,
            expiresIn,
            currentTime: new Date().toISOString(),
            expiresAt: new Date(Date.now() + expiresIn * 1000).toISOString()
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: "Failed to generate signed URL" });
    }
});

// Cleanup mechanism - run every 5 minutes
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes
let cleanupInterval;

function startCleanupScheduler() {
    cleanupInterval = setInterval(async () => {
        try {
            console.log('Running scheduled token cleanup...');
            const cleanedCount = await tokenManager.cleanupExpiredTokens();
            if (cleanedCount > 0) {
                console.log(`Scheduled cleanup completed: ${cleanedCount} tokens removed`);
            }
        } catch (error) {
            console.error('Error in scheduled cleanup:', error);
        }
    }, CLEANUP_INTERVAL);

    console.log(`Token cleanup scheduler started (runs every ${CLEANUP_INTERVAL / 1000} seconds)`);
}

function stopCleanupScheduler() {
    if (cleanupInterval) {
        clearInterval(cleanupInterval);
        console.log('Token cleanup scheduler stopped');
    }
}

// Graceful shutdown handling
process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT. Graceful shutdown...');
    stopCleanupScheduler();
    await tokenManager.disconnect();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM. Graceful shutdown...');
    stopCleanupScheduler();
    await tokenManager.disconnect();
    process.exit(0);
});

app.listen(3000, async () => {
    console.log("Server running on http://localhost:3000");
    console.log("One-time access control system enabled");

    // Initialize token manager connection
    try {

        // Start cleanup scheduler
        startCleanupScheduler();

    } catch (error) {
        console.error("Failed to initialize token manager:", error);
        console.log("Server will continue running but one-time access features may not work");
    }
});
