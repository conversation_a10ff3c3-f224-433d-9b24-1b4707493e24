import { getSignedUrl } from "@aws-sdk/cloudfront-signer";
import fs from "fs";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Load your private key
const privateKey = fs.readFileSync(process.env.CLOUDFRONT_PRIVATE_KEY_PATH, "utf8");

/**
 * Generate a signed CloudFront URL (internal use only)
 * @param {string} filePath - The path to the file (e.g., "/mydocs/file.pdf")
 * @param {number} expiresIn - Expiration time in seconds (default: 1 minute)
 * @returns {string} Signed URL
 */
function generateDirectSignedUrl(filePath, expiresIn = 60) {
    const url = `https://${process.env.CLOUDFRONT_DOMAIN}/${filePath}`;

    // Calculate expiration time more precisely, similar to Java Instant.now().plus()
    const now = new Date();
    const expirationTime = new Date(now.getTime() + (expiresIn * 1000));

    // Ensure we're working with a clean timestamp (remove milliseconds for consistency)
    expirationTime.setMilliseconds(0);

    console.log(`Generating signed URL:`);
    console.log(`  Current time: ${now.toISOString()}`);
    console.log(`  Expires at: ${expirationTime.toISOString()}`);
    console.log(`  Duration: ${expiresIn} seconds`);

    const signedUrl = getSignedUrl({
        url,
        keyPairId: process.env.CLOUDFRONT_PUBLIC_KEY_ID,
        privateKey,
        dateLessThan: expirationTime.toISOString(),  // Use ISO string like Java
    });

    return signedUrl;
}

/**
 * Generate a one-time access URL with token validation
 * @param {string} filePath - The path to the file (e.g., "/mydocs/file.pdf")
 * @param {number} expiresIn - Expiration time in seconds (default: 1 minute)
 * @param {string} baseUrl - Base URL of the application (e.g., "http://localhost:3000")
 * @returns {Promise<Object>} Object containing the one-time URL and token info
 */
export async function generateOneTimeUrl(filePath, expiresIn = 60, baseUrl = 'http://localhost:3000') {
    try {
        // Generate the actual CloudFront signed URL
        const signedUrl = generateDirectSignedUrl(filePath, expiresIn);

        // Generate a unique token for one-time access
        const token = tokenManager.generateToken(filePath, expiresIn);

        // Store the token with the signed URL
        await tokenManager.storeToken(token, filePath, signedUrl, expiresIn);

        // Create the one-time access URL that goes through our validation
        const oneTimeUrl = `${baseUrl}/access/${token}`;

        return {
            oneTimeUrl,
            token,
            filePath,
            expiresIn,
            expiresAt: new Date(Date.now() + expiresIn * 1000).toISOString(),
            directUrl: signedUrl // For debugging/admin purposes only
        };
    } catch (error) {
        console.error('Error generating one-time URL:', error);
        throw error;
    }
}

/**
 * Generate a one-time access URL using Lambda@Edge (RECOMMENDED)
 * @param {string} filePath - The path to the file (e.g., "/mydocs/file.pdf")
 * @param {number} expiresIn - Expiration time in seconds (default: 5 minutes)
 * @param {Object} metadata - Additional metadata to store with the token
 * @returns {Promise<Object>} Object containing the one-time URL and token info
 */
export async function generateOneTimeAccessUrl(filePath, expiresIn = 300, metadata = {}) {
    try {
        // Generate the actual CloudFront signed URL
        const signedUrl = generateDirectSignedUrl(filePath, expiresIn);

        // Create one-time access URL with Lambda@Edge token
        const oneTimeData = await edgeTokenManager.createOneTimeAccessUrl(
            filePath,
            signedUrl,
            expiresIn,
            metadata
        );

        return oneTimeData;
    } catch (error) {
        console.error('Error generating one-time access URL:', error);
        throw error;
    }
}

/**
 * Generate a signed CloudFront URL (legacy function for backward compatibility)
 * @param {string} filePath - The path to the file (e.g., "/mydocs/file.pdf")
 * @param {number} expiresIn - Expiration time in seconds (default: 1 minute)
 * @returns {string} Signed URL
 * @deprecated Use generateOneTimeAccessUrl for Lambda@Edge one-time access control
 */
export function generateSignedUrl(filePath, expiresIn = 60) {
    console.warn('generateSignedUrl is deprecated. Use generateOneTimeAccessUrl for Lambda@Edge one-time access control.');
    return generateDirectSignedUrl(filePath, expiresIn);
}
